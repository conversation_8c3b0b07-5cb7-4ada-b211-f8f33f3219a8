<!--
 * @Author: your name
 * @Date: 2021-11-08 15:09:05
 * @LastEditTime: 2021-12-24 11:21:09
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \health-hz\index.html
-->

<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <link href="https://cdnweb11.96225.com/favi.ico" mce_href="images/favicon.ico" rel="bookmark" type="image/x-icon" />
    <link href="https://cdnweb11.96225.com/favi.ico" mce_href="images/favicon.ico" rel="icon" type="image/x-icon" />
    <link href="https://cdnweb11.96225.com/favi.ico" mce_href="images/favicon.ico" rel="shortcut icon"
        type="image/x-icon" />

    <title>杭韵e家</title>

</head>
<% if (process.env.VUE_APP_RUN_ENV==='TEST' || process.env.VUE_APP_RUN_ENV==='stabletest' ) { %>
    <script type="text/javascript" src="./static/vconsole.min.js"></script>
    <script>
        var vConsole = new VConsole();
        console.log("TEST");
    </script>
    <% }%>
        <script>
            function setRem() {
                var html = document.documentElement;
                // var windowWidth = html.clientWidth > 768 ? 768:document.documentElement.clientWidth
                var windowWidth = html.clientWidth;
                html.style.fontSize = windowWidth / 3.75 + "px";
            }

            // web-view下的页面内
            function ready() {
                debugger
                console.log(window.__wxjs_environment === 'miniprogram') // true
                if (window.__wxjs_environment === 'miniprogram') {
                    window.sessionStorage.setItem('inMini', 'in')
                }
            }
            function loadScript(url) {
                var script = document.createElement("script");
                script.type = "text/javascript";
                script.src = url;
                document.head.appendChild(script);
            }
            /**
             * @param {String}  errorMessage   错误信息
             * @param {String}  scriptURI      出错的文件
             * @param {Long}    lineNumber     出错代码的行号
             * @param {Long}    columnNumber   出错代码的列号
             * @param {Object}  errorObj       错误的详细信息，Anything
             */
            /* window.onerror = function(errorMessage, scriptURI, lineNumber,columnNumber,errorObj) {
                            console.log("错误信息：" , errorMessage);
                            console.log("出错文件：" , scriptURI);
                            console.log("出错行号：" , lineNumber);
                            console.log("出错列号：" , columnNumber);
                            console.log("错误详情：" , errorObj);
                        } */
            window.addEventListener(
                "error",
                (e) => {
                    // console.log("addEventListener error", e);
                },
                true
            );

            document.writeln(
                '<script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"' +
                ">" +
                "<" +
                "/" +
                "script>"
            );



            document.addEventListener(
                "DOMContentLoaded",
                function () {
                    setRem();
                    window.onresize = function () {
                        // reported();
                        setRem();
                    };


                    wx.miniProgram.getEnv(function (res) {
                        console.log(res.miniprogram) // true

                        if (res.miniprogram) window.sessionStorage.setItem('inMini', 'in')

                    })

                    if (!window.WeixinJSBridge || !WeixinJSBridge.invoke) {
                        // debugger
                        document.addEventListener('WeixinJSBridgeReady', ready, false)
                    } else {
                        // debugger
                        ready()
                    }
                },
                false
            );



        </script>

        <body>
            <div id="app"></div>
            <!-- built files will be auto injected -->
            <!-- <script type="text/javascript" src="//res.wx.qq.com/open/js/jweixin-1.6.0.js"></script> -->
        </body>

</html>