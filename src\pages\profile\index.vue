<template>
    <div
        style="
            width: 100%;
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
            background: #faf9f7;
        "
    >
        <!-- 头部用户信息 -->
        <div class="header">
            <div class="box">
                <van-uploader
                    :max-size="2 * 1024 * 1024"
                    @oversize="onOversize"
                    :before-read="beforeRead"
                    :after-read="afterRead"
                    v-if="userInfo.userId"
                >
                    <img
                        class="user-avatar"
                        @error="handleErrImg"
                        :src="
                            userInfo.avatar ||
                            require('@/images/activity/man.png')
                        "
                    />
                </van-uploader>
                <img
                    v-else
                    @click="gologin"
                    class="user-avatar"
                    :src="
                        userInfo.avatar || require('@/images/activity/man.png')
                    "
                />

                <div class="userInfo">
                    <!-- 未登录 -->
                    <template v-if="!userInfo.userId">
                        <p @click="gologin">登录/注册</p>
                        <p class="tip" @click="gologin">登录后获取更多信息</p>
                    </template>

                    <!-- 已经登录 -->
                    <template v-if="userInfo.userId">
                        <p>{{ userInfo.blurName }}</p>
                        <p class="tip">{{ userInfo.blurMobile }}</p>
                    </template>
                </div>

                <!-- 扫码签到入口--登录后才显示 -->
                <div v-if="userInfo.userId" class="sys" @click="showAc">
                    <img class="sysIcon" src="@/images/activity/sys.png" />
                    <p style="margin-top: 6px">扫码签到</p>
                </div>
            </div>
        </div>

        <div class="menuCont">
            <div class="bg"></div>
            <div class="menu" @click="goToMyActivities">
                <i class="icon myAc"></i>
                <span class="menuP">我的活动</span>
                <i class="arrow"></i>
            </div>
            <div class="line"></div>
            <div class="menu" @click="showKF">
                <i class="icon kfzx"></i>
                <span class="menuP">客服咨询</span>
                <i class="arrow"></i>
            </div>
            <div class="line"></div>
            <div class="menu" @click="showXYSM">
                <i class="icon xysm"></i>
                <span class="menuP">协议与说明</span>
                <i class="arrow"></i>
            </div>
            <div class="line"></div>
            <div class="menu" @click="showAbout">
                <i class="icon aboutUs"></i>
                <span class="menuP">关于我们</span>
                <i class="arrow"></i>
            </div>
        </div>

        <!-- 退出登录 -->
        <div class="logout-section" v-if="userInfo.userId">
            <button class="logoutBtn" @click="handleLogout">退出登录</button>
        </div>

        <acSignIn ref="acSignIn" />
    </div>
</template>

<script>
import Vue from "vue";
import { Dialog, Toast, Uploader } from "vant";
import { upload, updateAvatar } from "@/api/my";
import acSignIn from "@/components/actSignIn/index.vue";
import util from "@/util/util";
Vue.use(Toast).use(Uploader);

export default {
    components: {
        acSignIn,
    },
    name: "Profile",
    data() {
        return {
            userInfo: {
                avatar: "",
                userId: "",
                blurMobile: "",
                blurName: "",
            },
            defaultImage: require("@/images/activity/man.png"),
        };
    },
    mounted() {
        // console.log(222222);
        // alert(1);

        this.loadUserInfo();
        // 如果链接上有参数scanCode  调用扫码签到组件的签到方法
        if (util.getQueryStringHash("scanCode")) {
            this.$refs.acSignIn.signAc(
                util.getQueryStringHash("scanCode"),
                util.getQueryStringHash("acId")
            );
        }
        // this.$nextTick(() => {
        // if (util.isInMini()) {
        //     document.title = "杭韵e家";
        // } else {
        //     document.title = "个人中心";
        // }
        // });
    },
    methods: {
        onOversize(file) {
            console.log(file);
            Toast("图片大小不能超过 2M");
        },
        handleErrImg(event) {
            event.target.src = this.defaultImage;
            // 控制不要一直跳动
            event.target.onerror = null;
        },
        showAc() {
            // 扫码签到组件的展示待签到活动弹窗
            this.$refs.acSignIn.showDia();
        },
        isLogin() {
            if (this.userInfo.userId) return true;
            this.gologin();
            return false;
        },
        goToMyActivities() {
            if (this.isLogin()) {
                util.jump("#/myActivities");
            }
        },
        showKF() {
            util.jump("#/kfzx");
        },
        showXYSM() {
            util.jump("#/privacy");
        },
        gologin() {
            // 设置下acurl
            // 保存当前路由路径，而不是完整URL
            const currentPath = window.location.hash || "#/";
            window.sessionStorage.setItem("acURL", currentPath);
            util.jump("#/login");
        },
        beforeRead(file) {
            debugger;
            if (!/^image\/.*$/i.test(file.type)) {
                Toast("请选择图片~");
                return false;
            }
            return true;
        },
        afterRead(file) {
            debugger;
            var fromData = new FormData();
            fromData.append("file", file.file);
            upload(fromData).then((r) => {
                if (r && r.code == 200) {
                    // 上传成功
                    updateAvatar({ avatar: r.data.url }).then((r_) => {
                        if (r_ && r_.code == 200) {
                            Toast.success("修改成功");
                            this.userInfo.avatar = `${
                                process.env.VUE_APP_URL + r.data.url
                            }`;
                            // 修改缓存里的用户信息
                            let u = JSON.parse(
                                window.localStorage.getItem("userInfo")
                            );
                            u.avatar = r.data.url;
                            window.localStorage.setItem(
                                "userInfo",
                                JSON.stringify(u)
                            );
                        }
                    });
                }
                // this.$set(
                //     this.userInfo,
                //     "avatar",
                //     `${process.env.VUE_APP_URL + r.data.url}`
                // );
            });
        },
        showAbout() {
            util.openDialogAlert(
                "温馨提示",
                "南京银行杭州分行<br>杭州市拱墅区凤起路432号",
                null,
                "关闭"
            );
        },
        loadUserInfo() {
            // TODO: 从store或API获取用户信息
            // const userInfo = this.$store.state.userInfo;
            if (window.localStorage.getItem("userInfo")) {
                let u_ = JSON.parse(window.localStorage.getItem("userInfo"));
                this.userInfo = { ...u_ };
                this.userInfo.avatar = `${
                    process.env.VUE_APP_URL + this.userInfo.avatar
                }`;
            }
        },

        handleLogout() {
            Dialog.confirm({
                title: "温馨提示",
                message: "确认要退出当前账号吗？",
                confirmButtonText: "确认退出",
                confirmButtonColor: "#07C160",
                cancelButtonText: "暂不退出",
                cancelButtonColor: "#000000",
            })
                .then(() => {
                    // TODO: 实现退出登录逻辑
                    this.userInfo = { avatar: "", userId: "" };
                    util.loginOut(1);
                })
                .catch(() => {
                    // 用户取消
                });
        },
    },
};
</script>

<style lang="less" scoped>
.header {
    width: 100%;
    height: 139px;
    background: linear-gradient(180deg, #e6c496 0%, #f5ebdf 100%),
        linear-gradient(312deg, #f4e8d9 0%, #edcea5 69%, #f8f1e8 100%);
    .box {
        padding: 0.35rem 0.22rem 0;
        display: flex;
        align-items: center;
        .user-avatar {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            margin-right: 13px;
        }
        .userInfo {
            font-weight: 500;
            font-size: 18px;
            color: #2f2f2f;
            line-height: 25px;
            text-align: left;
            font-style: normal;
            width: 65%;
            .tip {
                margin-top: 6px;
                font-size: 11px;
                color: #966233;
                line-height: 15px;
                text-align: left;
                font-style: normal;
            }
        }
        .sys {
            display: flex;
            flex-direction: column;
            align-items: center;
            .sysIcon {
                width: 23px;
                height: 23px;
            }
            font-size: 9px;
            color: #2f2f2f;
            line-height: 13px;
            text-align: right;
        }
    }
}

.menuCont {
    width: 100%;
    // background: linear-gradient(180deg, #f3e5d2 0%, #faf9f7 100%),
    //     linear-gradient(180deg, #ffffff 0%, #faf9f7 100%, #faf9f7 100%);
    // background: linear-gradient(180deg, #ffffff 0%, #faf9f7 100%, #faf9f7 100%),
    //     linear-gradient(180deg, #f3e5d2 0%, rgba(255, 255, 255, 0) 100%);
    border-radius: 8px;
    margin-top: 12px;
    position: relative;
    z-index: 1;
    overflow: hidden;
    .bg {
        position: absolute;
        z-index: 1;
        width: 100%;
        height: 150px;
        // background: linear-gradient(180deg, #f3e5d2 0%, #faf9f7 100%),
        //     linear-gradient(180deg, #ffffff 0%, #faf9f7 100%, #faf9f7 100%);
        background: linear-gradient(
            180deg,
            #f3e5d2 0%,
            #fdfbf8 40%,
            #faf9f7 100%
        );
    }
    .menu {
        position: relative;
        z-index: 2;
        display: flex;
        // padding-left: 28px;
        padding-top: 20px;
        padding-bottom: 15px;
        // padding-right: 27px;
        align-items: center;
        width: 319px;
        // height: 54px;
        background: rgba(255, 255, 255, 0);
        margin: 0 auto;
        // border-bottom: 1px solid #f8eee2;
        .icon {
            width: 22px;
            height: 22px;
            margin-right: 10px;
        }
        .myAc {
            background: url("@/images/activity/myAc.png") no-repeat center;
            background-size: contain;
        }
        .kfzx {
            background: url("@/images/activity/kfzx.png") no-repeat center;
            background-size: contain;
        }
        .xysm {
            background: url("@/images/activity/xysm.png") no-repeat center;
            background-size: contain;
        }
        .aboutUs {
            background: url("@/images/activity/aboutUs.png") no-repeat center;
            background-size: contain;
        }

        .menuP {
            font-size: 13px;
            color: #2f2f2f;
            width: 90%;
        }
        .arrow {
            width: 12px;
            height: 12px;
            background: url("@/images/activity/myArr.png") no-repeat center;
            background-size: contain;
        }
    }
    .line {
        position: relative;
        z-index: 2;
        width: 319px;
        height: 1px;
        background: #f8eee2;
        margin: 0 auto;
    }
}

.logout-section {
    width: 100%;
    position: fixed;
    bottom: 130px;

    .logoutBtn {
        width: 318px;
        height: 42px;
        border-radius: 21px;
        border: 1px solid #d6bca4;
        font-weight: 500;
        font-size: 18px;
        color: #c28d4b;
        background: none;
        box-sizing: content-box;
        display: block;
        margin: 0 auto;
    }
}
</style>
