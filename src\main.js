/*
 * @Author: Activity H5 Project
 * @Date: 2025-01-01
 * @LastEditTime: 2025-01-01
 * @Description: 杭韵e家活动页面主入口文件
 */

import Vue from "vue";
import App from "./App";
import router from "./router";
import store from "./store";
import "./css/reset.css";

// 基础插件
import VueCookies from "vue-cookies";
import VueLazyload from "vue-lazyload";
import VueClipboard from "vue-clipboard2";

Vue.use(VueCookies);
Vue.use(VueLazyload, {
    preLoad: 1.3,
    attempt: 1
});
Vue.use(VueClipboard);

// 工具函数
import common from "./util/common";
import tools from "./util/tools";

// API接口
import api from "./api/api";
import * as activityApi from "./api/activity";

// 全局配置
Vue.config.productionTip = false;

// 挂载API到Vue原型
Vue.prototype.$api = {
    ...api,
    ...activityApi
};

// 全局工具函数
Vue.prototype.$common = common;
Vue.prototype.$tools = tools;

// 应用初始化
async function initApp() {
    try {
        // 恢复用户信息
        await store.dispatch('restoreUserInfo');

        // 恢复搜索历史
        await store.dispatch('restoreSearchHistory');

        // 获取位置信息（如果有）
        const latitude = common.getUrlParam("latitude");
        const longitude = common.getUrlParam("longitude");
        if (latitude && longitude) {
            store.dispatch('updateLocation', { latitude, longitude });
        }

        console.log('应用初始化完成');
    } catch (error) {
        console.error('应用初始化失败:', error);
    }
}

// 路由守卫
router.beforeEach(async (to, from, next) => {
    // 设置页面标题
    if (!common.isInMini()) {
        document.title = to.meta.title || '杭韵e家';
    }
    // 检查是否需要登录
    if (to.meta.needLogin && !store.getters.isLoggedIn) {
        // TODO: 跳转到登录页面或触发登录流程
        console.log('需要登录');
    }

    next();
});

// 初始化应用
initApp();


if (common.getUrlParam("origin") == 'hyejMini') window.sessionStorage.setItem('inMini', 'in')

// 创建Vue实例
new Vue({
    el: "#app",
    router,
    store,
    components: { App },
    template: "<App/>"
});
