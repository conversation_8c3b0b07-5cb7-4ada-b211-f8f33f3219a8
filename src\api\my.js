import http from "./http";
import util from "../util/util";

export const upload = (data) => {
    debugger
    return http('/activity-manager-api/file/upload/file', data, "post")
        .then((response) => {
            debugger
            return response;
        })
        .catch((err) => {
            debugger
        });
};


export const updateAvatar = (data) => {
    debugger
    return http('/activity-manager-api/app/user/updateAvatar', data, "post")
        .then((response) => {
            return response;
        })
        .catch((err) => {
            debugger
        });
};


export const getMyActivities = (data) => {
    debugger
    return http('/activity-manager-api/app/activity/myActivities', data, "post")
        .then((response) => {
            return response;
        })
        .catch((err) => {
            debugger
        });
};

// 待签到的活动列表
export const getsignAct = (data) => {
    debugger
    return http('/activity-manager-api/app/activity/signAct', data, "get")
        .then((response) => {
            return response;
        })
        .catch((err) => {
            debugger
        });
};

// 签到

export const acsignScan = (data) => {
    debugger
    return http('/activity-manager-api/app/activity/signScan', data, "post", null, true, "签到中")
        .then((response) => {
            return response;
        })
    // .catch((err) => {
    //     debugger
    //     // 提示错误
    //     util.openDialogAlert("签到失败", err || "", null, "关闭", '', "ovSelfClass");
    // });
};



