<template>
  <div class="registration-page">
    <!-- 导航栏 -->
    <!-- <van-nav-bar
      title="活动报名"
      left-arrow
      @click-left="goBack"
    /> -->

    <!-- 活动信息部分 -->
    <div class="activity-info-section">
      <div class="section-header">
        <div class="header-left">
          <div class="orange-bar"></div>
          <span class="section-title">活动信息</span>
        </div>
      </div>
      
      <!-- 收起状态：只显示前2条关键信息 -->
      <div class="activity-info-content" v-show="!isActivityInfoExpanded">
        <div class="info-item">
          <span class="label">活动名称：</span>
          <span class="value">{{ activity.actTitle || 'XXXXXXXXXXXXXXXXXXXXXX' }}</span>
        </div>
        <div class="info-item">
          <span class="label">活动时间：</span>
          <span class="value">{{ activity.actTime || '时间待定' }}</span>
        </div>
        <div class="expand-trigger" @click="toggleActivityInfo">
          <van-icon 
            :name="isActivityInfoExpanded ? 'arrow-up' : 'arrow-down'" 
            class="expand-icon"
          />
        </div>
      </div>
      
      <!-- 展开状态：显示所有信息 -->
      <div class="activity-info-content" v-show="isActivityInfoExpanded">
        <div class="info-item">
          <span class="label">活动名称：</span>
          <span class="value">{{ activity.actTitle || 'XXXXXXXXXXXXXXXXXXXXXX' }}</span>
        </div>
        <div class="info-item">
          <span class="label">活动时间：</span>
          <span class="value">{{ activity.actTime || '时间待定' }}</span>
        </div>
        <div class="info-item">
          <span class="label">活动地址：</span>
          <span class="value">{{ activity.locationString || '地址待定' }}</span>
        </div>
        <div class="info-item">
          <span class="label">活动须知：</span>
          <span class="value">{{ activity.actNotice || '暂无须知' }}</span>
        </div>
        <div class="info-item" v-if="activity.actDesc">
          <span class="label">活动描述：</span>
          <span class="value">{{ activity.actDesc }}</span>
        </div>
        <div class="expand-trigger" @click="toggleActivityInfo">
          <van-icon 
            :name="isActivityInfoExpanded ? 'arrow-up' : 'arrow-down'" 
            class="expand-icon"
          />
        </div>
      </div>
    </div>

    <!-- 报名信息部分 -->
    <div class="registration-form-section">
      <div class="section-header">
        <div class="header-left">
          <div class="orange-bar"></div>
          <span class="section-title">请填写下列报名信息</span>
        </div>
      </div>

      <div class="registration-form">
        <!-- 动态字段 -->
        <div class="form-group">
          <div 
            v-for="field in allFields" 
            :key="field.id"
            class="dynamic-field"
          >
            <!-- 文本输入框 -->
            <van-field
              v-if="field.type === 'text'"
              v-model="formData[field.id]"
              :placeholder="`请输入${field.label}`"
              :error-message="fieldErrors[field.id]"
              @blur="validateField(field.id)"
            >
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
            </van-field>
            
            <!-- 多行文本框 -->
            <van-field
              v-else-if="field.type === 'textarea'"
              v-model="formData[field.id]"
              :placeholder="`请输入${field.label}`"
              type="textarea"
              rows="3"
              :error-message="fieldErrors[field.id]"
              @blur="validateField(field.id)"
            >
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
            </van-field>
            
            <!-- 性别特殊处理 - 单选按钮 -->
            <van-field v-else-if="field.id === 'gender'" :error-message="fieldErrors.gender">
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
              <template #input>
                <div class="gender-options">
                  <label class="radio-option" v-for="option in field.options" :key="option">
                    <input 
                      type="radio" 
                      :value="option" 
                      v-model="formData.gender"
                      @change="validateField('gender')"
                    />
                    <span class="radio-label">{{ option }}</span>
                  </label>
                </div>
              </template>
            </van-field>
            
            <!-- 选择器 -->
            <van-field
              v-else-if="field.type === 'select'"
              v-model="formData[field.id]"
              :placeholder="`请选择${field.label}`"
              readonly
              is-link
              @click="showFieldPicker(field)"
              :error-message="fieldErrors[field.id]"
            >
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
            </van-field>
          </div>
        </div>

      </div>
    </div>

    <!-- 提交按钮 -->
    <div class="submit-section">
      <van-button
        type="primary"
        size="large"
        :loading="submitting"
        :disabled="submitting"
        @click="submitRegistration"
        class="submit-button"
      >
        {{ submitting ? '提交中...' : '提交报名' }}
      </van-button>
    </div>

    <!-- 选择器弹窗 -->


    <!-- 自定义字段选择器 -->
    <van-popup
      v-model="showCustomFieldPickerDialog"
      position="bottom"
      round
      :close-on-click-overlay="true"
    >
      <van-picker
        :columns="currentCustomFieldOptions"
        :title="currentCustomField ? `请选择${currentCustomField.label}` : '请选择'"
        show-toolbar
        @confirm="onCustomFieldConfirm"
        @cancel="onCustomFieldCancel"
      />
    </van-popup>

    <!-- 成功提示弹窗 -->
    <van-dialog
      v-model="showSuccessDialog"
      title="温馨提示"
      class="success-dialog"
      :show-confirm-button="false"
      :show-cancel-button="false"
    >
      <div class="dialog-message">
        <div>恭喜您，报名成功。请在活动开始后</div>
        <div>前往活动地点完成签到并参加活动！</div>
      </div>
      <div class="dialog-footer" @click="onSuccessConfirm">
        知道了
      </div>
    </van-dialog>
  </div>
</template>

<script>
import Vue from 'vue';
import {
  NavBar,
  Icon,
  Button,
  Field,
  Stepper,
  Popup,
  Picker,
  Dialog,
  Toast,
  ActionSheet
} from 'vant';

Vue.use(NavBar)
  .use(Icon)
  .use(Button)
  .use(Field)
  .use(Stepper)
  .use(Popup)
  .use(Picker)
  .use(Dialog)
  .use(Toast)
  .use(ActionSheet);

export default {
  name: 'ActivityRegistration',
  data() {
    return {
      activity: {},
      isActivityInfoExpanded: false, // 默认折叠
      submitting: false,
      showSuccessDialog: false,
      
      // 表单数据
      formData: {
        name: '',
        phone: '', // 默认取登录人员手机号，不支持修改
        cardType: '',
        idCard: '',
        age: '',
        gender: '',
        height: '',
        weight: '',
        education: '',
        adultCount: 0,
        childCount: 0,
        customFields: {}
      },
      
      // 字段错误信息
      fieldErrors: {},
      
      // 选择器相关
      showCustomFieldPickerDialog: false,
      currentCustomField: null,
      
      // 选项数据
      cardTypeOptions: ['身份证', '护照', '港澳通行证', '台胞证'],
      genderOptions: ['男', '女', '其他'],
      educationOptions: ['小学', '初中', '高中', '大专', '本科', '硕士', '博士'],
      
      // 自定义字段
      customFields: [],
      maxFamilyMembers: 5, // 携带人数上限
      
      // 字段必填配置（从后端获取）
      fieldRequiredConfig: {
        name: true,        // 姓名必填
        phone: true,       // 手机号必填（默认填充，不支持修改）
        cardType: false,   // 证件类型
        idCard: true,      // 身份证号码必填
        age: true,         // 年龄必填
        gender: true,      // 性别必填
        height: false,     // 身高
        weight: false,     // 体重
        education: false,  // 学历
        familyMembers: false // 携带亲属
      }
    };
  },
  
  computed: {
    currentCustomFieldOptions() {
      if (!this.currentCustomField) return [];
      return this.currentCustomField.options || [];
    },

    // 所有字段（包括预设字段和自定义字段）
    allFields() {
      return this.customFields;
    },

  },
  
  mounted() {
    this.loadActivityData();
    this.loadRegistrationConfig();
    this.loadUserInfo();
  },
  
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    
    // 加载用户信息
    loadUserInfo() {
      try {
        // 从localStorage获取用户信息
        const userInfoStr = localStorage.getItem('userInfo');
        if (userInfoStr) {
          const userInfo = JSON.parse(userInfoStr);
          // 只预填充手机号到表单，从blurMobile字段获取
          if (userInfo.blurMobile) {
            this.formData.phone = userInfo.blurMobile;
          }
        }
      } catch (error) {
        console.error('加载用户信息失败:', error);
      }
    },
    
    // 加载活动数据
    async loadActivityData() {
      try {
        const activityId = this.$route.params.id;
        const response = await this.$api.getActivityDetail(activityId);
        
        if (response) {
          
          // 安全地获取数据，处理不同的数据结构
          const data =  response.data ;
          
          if (data) {
            this.activity = {
              id: activityId,
              actTitle: data.actTitle|| '活动名称',
              actTime: data.actTime|| '时间待定',
              locationString: data.locationString || '地址待定',
              actNotice: data.actNotice|| '暂无须知',
              actDesc: data.actDesc || ''
            };

            // 处理活动用户字段配置
            if (data.activityUserDTOList && Array.isArray(data.activityUserDTOList)) {
              this.processActivityUserFields(data.activityUserDTOList);
            }
          } else {
            console.warn('接口返回数据为空');
          }
        } else {
          console.warn('接口返回为空');
        }
      } catch (error) {
        console.error('加载活动数据失败:', error);
      }
    },
    
    // 处理活动用户字段配置
    processActivityUserFields(activityUserDTOList) {
      try {
        const allFields = [];

        activityUserDTOList.forEach(field => {
          const customField = this.convertToCustomField(field);
          if (customField) {
            // 将 mobile 字段映射到 phone 字段
            if (customField.id === 'mobile') {
              customField.id = 'phone';
            }
            allFields.push(customField);
          }
        });

        this.customFields = allFields;

        console.log('所有字段已更新:', this.customFields);
        console.log('原始接口数据:', activityUserDTOList);
      } catch (error) {
        console.error('处理活动用户字段配置失败:', error);
      }
    },


    // 将接口字段转换为自定义字段格式
    convertToCustomField(field) {
      try {
        const fieldType = this.getFieldTypeFromNumber(field.type);
        
        return {
          id: field.key,
          label: field.title,
          type: fieldType,
          required: field.required === true,
          options: field.text ? field.text.split(/[,，]/) : null,
          result: field.result || null
        };
      } catch (error) {
        console.error('转换自定义字段失败:', error);
        return null;
      }
    },

    // 根据数字类型转换为字段类型
    getFieldTypeFromNumber(typeNumber) {
      const typeMap = {
        0: 'text',      // 文本
        1: 'textarea',  // 多行文本框
        2: 'select'     // 选择
      };
      return typeMap[typeNumber] || 'text';
    },

    // 加载报名配置
    async loadRegistrationConfig() {
      try {
        // 报名配置现在通过activityUserDTOList处理，这里保留空实现
        // 如果需要额外的配置，可以在这里添加
      } catch (error) {
        console.error('加载报名配置失败:', error);
      }
    },
    
    // 加载字段必填配置
    async loadFieldRequiredConfig() {
      try {
        // 字段必填配置现在通过activityUserDTOList在loadActivityData中处理
        // 这里保留空实现，如果需要额外的配置可以在这里添加
        console.log('字段必填配置已通过activityUserDTOList处理');
      } catch (error) {
        console.error('加载字段必填配置失败:', error);
      }
    },
    
    // 切换活动信息展开/折叠
    toggleActivityInfo() {
      this.isActivityInfoExpanded = !this.isActivityInfoExpanded;
    },
    
    
    // 显示字段选择器
    showFieldPicker(field) {
      // 确保字段有选项数据
      if (!field || !field.options || field.options.length === 0) {
        Toast('选项数据加载失败');
        return;
      }

      this.currentCustomField = field;
      this.showCustomFieldPickerDialog = true;
    },

    
    
    // 验证单个字段
    validateField(fieldName) {
      const value = this.formData[fieldName];
      const field = this.customFields.find(f => f.id === fieldName);
      let error = '';
      
      // 如果是必填字段且值为空
      if (field && field.required && (!value || !value.trim())) {
        error = `请填写${field.label}`;
      }
      
      // 特殊字段验证
      if (value && value.trim()) {
        switch (fieldName) {
          case 'idCard':
            if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
              error = '身份证号码格式不正确';
            }
            break;
          case 'age':
            if (isNaN(value) || value < 1 || value > 120) {
              error = '请输入有效的年龄';
            }
            break;
          case 'height':
            if (isNaN(value) || value < 50 || value > 250) {
              error = '请输入有效的身高';
            }
            break;
          case 'weight':
            if (isNaN(value) || value < 10 || value > 300) {
              error = '请输入有效的体重';
            }
            break;
        }
      }
      
      this.$set(this.fieldErrors, fieldName, error);
      return !error;
    },
    
    
    // 验证携带亲属
    validateFamilyMembers() {
      const total = this.formData.adultCount + this.formData.childCount;
      let error = '';
      
      if (total > this.maxFamilyMembers) {
        error = `携带人数不能超过${this.maxFamilyMembers}人`;
        this.formData.adultCount = Math.min(this.formData.adultCount, this.maxFamilyMembers);
        this.formData.childCount = Math.min(this.formData.childCount, this.maxFamilyMembers - this.formData.adultCount);
      }
      
      this.$set(this.fieldErrors, 'familyMembers', error);
      return !error;
    },
    

    
    // 显示自定义字段选择器
    showCustomFieldPicker(field) {
      // 确保字段有选项数据
      if (!field || !field.options || field.options.length === 0) {
        Toast('选项数据加载失败');
        return;
      }

      this.currentCustomField = field;
      this.showCustomFieldPickerDialog = true;
    },
    
    onCustomFieldConfirm(value, index) {
      try {
        // 处理不同的参数格式
        let selectedValue = value;

        // 如果 value 是数组（多列选择器的情况）
        if (Array.isArray(value) && value.length > 0) {
          selectedValue = value[0];
        }

        // 如果 selectedValue 是对象，提取实际值
        if (typeof selectedValue === 'object' && selectedValue !== null) {
          selectedValue = selectedValue.value || selectedValue.text || selectedValue.name || selectedValue;
        }

        if (this.currentCustomField && selectedValue !== undefined && selectedValue !== null) {
          this.$set(this.formData, this.currentCustomField.id, selectedValue);
          this.validateField(this.currentCustomField.id);
        }
      } catch (error) {
        console.error('处理选择器确认事件时出错:', error);
      } finally {
        // 确保弹框关闭
        this.showCustomFieldPickerDialog = false;
        this.currentCustomField = null;
      }
    },
    

    onCustomFieldCancel() {
      this.showCustomFieldPickerDialog = false;
      this.currentCustomField = null;
    },
    
    // 提交报名
    async submitRegistration() {
      // 防止重复提交
      if (this.submitting) return;
      
      // 验证所有必填项
      let hasError = false;
      
      // 验证所有字段
      this.customFields.forEach(field => {
        if (!this.validateField(field.id)) {
          hasError = true;
        }
      });
      
      if (hasError) {
        Toast('请填写带*的项目');
        return;
      }
      
      this.submitting = true;
      
      try {
        // 构建提交数据
        const submitData = {
          id: this.activity.id,                    // 活动Id
          name: this.formData.name || '',          // 姓名
          phone: this.formData.phone || '',        // 手机号
          cardType: this.formData.cardType || '',  // 证件类型
          idCard: this.formData.idCard || '',      // 身份证号
          gender: this.formData.gender || '',      // 性别
          age: this.formData.age || '',            // 年龄
          human: this.formData.adultCount || 0,    // 携带成人数
          child: this.formData.childCount || 0,    // 携带儿童数
          high: this.formData.height || '',        // 身高
          weight: this.formData.weight || '',      // 体重
          educate: this.formData.education || '',  // 学历
          community: '',                           // 社区
          address: '',                             // 地址
          selfAdds: this.buildSelfAdds()           // 自增项目
        };
        
        // 调试：打印提交的数据
        console.log('提交的报名数据:', submitData);
        console.log('selfAdds 数据:', submitData.selfAdds);
        
        // 调用报名接口
        const response = await this.$api.registerActivity(submitData);
        
        if (response && response.code === 200) {
          this.showSuccessDialog = true;
        } else {
          Toast(response?.respDesc || '报名失败，请重试');
        }
      } catch (error) {
        console.error('报名失败:', error);
        Toast('报名失败，请重试');
      } finally {
        this.submitting = false;
      }
    },
    
    // 构建自增项目数据
    buildSelfAdds() {
      const selfAdds = [];
      
      // 遍历所有字段，构建自增项目
      this.customFields.forEach(field => {
        const value = this.formData[field.id];
        if (value && value.trim()) {
          selfAdds.push({
            title: field.label,           // 字段标题
            result: value,                // 字段值
            key: field.id,                // 字段key
            type: this.getFieldType(field.type), // 字段类型
            required: field.required || false,   // 是否必填
            text: field.options ? field.options.join(',') : null, // 选项文本
            human: null,                  // 成人数
            child: null                   // 儿童数
          });
        }
      });
      
      return selfAdds;
    },
    
    // 获取字段类型数字
    getFieldType(type) {
      const typeMap = {
        'text': 0,      // 文本
        'textarea': 0,  // 文本域
        'select': 2,    // 选择
        'number': 1     // 数字
      };
      return typeMap[type] || 0;
    },
    
    // 成功确认
    onSuccessConfirm() {
      this.showSuccessDialog = false;
      // 返回来源页
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="less" scoped>
.registration-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

.activity-info-section,
.registration-form-section {
  background: white;
  margin: 8px 16px;
  border-radius: 6px;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 16px;
  position: relative;
  
  .header-left {
    display: flex;
    align-items: center;
    width: 100%;
    
    .orange-bar {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4.5px;
      height: 15px;
      background: #C28D4B;
      border-radius: 2.25px;
      margin-right: 3px;
    }
    
    .section-title {
      font-family: 'PingFang SC Semibold', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
      font-weight: 600;
      font-size: 16px;
      color: #373737;
      margin-left: 3px;
      text-align: left;
    }
  }
}

.activity-info-content {
  padding: 0 16px 16px;
  
  .info-item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    
    .label {
      font-family: 'PingFang SC', 'PingFang SC Medium', 'Microsoft YaHei', Arial, sans-serif;
      min-width: 80px;
      font-size: 14px;
      color: #373737;
      flex-shrink: 0;
    }
    
    .value {
      flex: 1;
      font-size: 14px;
      color: #333;
      word-break: break-all;
      line-height: 1.4;
      text-align: right;
    }
  }
  
  .expand-trigger {
    display: flex;
    justify-content: center;
    padding: 12px 0;
    border-top: 1px solid #f0f0f0;
    cursor: pointer;
    
    .expand-icon {
      color: #999;
      font-size: 16px;
    }
  }
}

.registration-form {
  padding: 3px;
  
  .form-group {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .unit {
    color: #999;
    font-size: 14px;
  }
  
  .stepper-group {
    display: flex;
    gap: 20px;
    
    .stepper-item {
      display: flex;
      align-items: center;
      gap: 4px;
      
      .stepper-label {
        font-family: 'PingFang SC', 'PingFang SC Medium', 'Microsoft YaHei', Arial, sans-serif;
        font-size: 14px;
        color: #373737;
        min-width: 15px;
      }
    }
  }
  
  // 修改步进器内部布局，让输入框和标签在同一行
  :deep(.van-stepper) {
    display: flex;
    align-items: center;
    
    .van-stepper__input {
      width: 25px;
      height: 20px;
      font-size: 11px;
      margin: 0 3px;
    }
    
    .van-stepper__button {
      width: 20px;
      height: 20px;
      font-size: 11px;
    }
    
    .van-stepper__minus,
    .van-stepper__plus {
      width: 20px;
      height: 20px;
      font-size: 11px;
    }
  }
  
  .custom-field {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .gender-options {
    display: flex;
    gap: 24px;
    
    .radio-option {
      display: flex;
      align-items: center;
      cursor: pointer;
      
      input[type="radio"] {
        margin-right: 8px;
        width: 14px;
        height: 14px;
        accent-color: #1989fa;
      }
      
      .radio-label {
        font-size: 13px;
        color: #333;
      }
    }
  }
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: white;
  border-top: 1px solid #f0f0f0;
  
  .submit-button {
    width: 100%;
    height: 48px;
    background: linear-gradient(90deg, #C08A48 0%, #EDC391 100%);
    border: none;
    border-radius: 24px;
    font-size: 16px;
    font-weight: 500;
    color: white;
  }
}

:deep(.van-field) {
  margin-bottom: 16px;
}

:deep(.van-field__label) {
  color: #333;
  font-size: 14px;
  text-align: left;
  justify-content: flex-start;
  width: 80px;
  min-width: 80px;
  flex-shrink: 0;
  
  // 必填星号样式
  .required-star {
    color: #ff6b35;
    font-weight: bold;
    margin-left: 4px;
  }
}

:deep(.van-field__control) {
  font-size: 14px;
}

// 单行文本框右对齐
:deep(.van-field:not(.van-field--textarea) .van-field__control) {
  text-align: right;
}

// 多行文本框保持左对齐
:deep(.van-field--textarea .van-field__control) {
  text-align: left;
}

:deep(.van-field__error-message) {
  color: #ff6b35;
  font-size: 12px;
}

:deep(.van-stepper) {
  .van-stepper__input {
    width: 35px;
    height: 28px;
    font-size: 13px;
  }
  
  .van-stepper__button {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
}

// 禁用字段样式
.disabled-field {
  :deep(.van-field__control) {
    color: #999 !important;
    background-color: #f5f5f5 !important;
  }
}

// 成功弹窗样式
:deep(.success-dialog) {
  .van-dialog {
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #ffb3ba; // 粉色边框
    display: flex;
    flex-direction: column;
  }
  
  .van-dialog__header {
    padding: 20px 20px 10px;
    font-weight: 521;
    font-size: 16px !important;
    color: #333;
    text-align: center;
    flex-shrink: 0;
  }
  
  .van-dialog__content {
    padding: 10px 0px 0px;
    flex: 1;
  }
  
  .dialog-message {
    font-size: 15px;
    line-height: 1.5;
    color: #666;
    text-align: center;
    margin-bottom: 18px;
    div {
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .dialog-footer {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px;
    color: #07c160;
    font-size: 16px;
    font-weight: 400;
    cursor: pointer;
    border-top: 1px solid #f0f0f0;
    box-sizing: border-box;
    flex-shrink: 0;
    margin: 0;
    
    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
