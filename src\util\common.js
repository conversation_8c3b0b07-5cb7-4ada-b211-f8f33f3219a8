/*
 * @Descripttion:
 * @version:
 * @Author: ch<PERSON><PERSON>
 * @Date: 2019-04-02 14:29:27
 * @LastEditors: DESKTOP-TJTC9EU
 * @LastEditTime: 2023-08-03
 */
import { sha256 } from "js-sha256";
import CryptoJS from "crypto-js";
import { Toast } from "vant";
const common = {
    evn: "build", //dev开发模式，模拟登录，不加载cordova    build生产模式
    tips: {
        noResp: "网络连接超时，请稍后再试~",
        wait: "当前预约人数过多，请稍后再试~",
        hf: '<p style="line-height:1.3;">亲爱的，您操作太过频繁，<br>请稍后再试~</p>',
        noNet: "没有网络，请看下是否连上网络了~",
    },
    paperObj: {
        "01": "居民身份证",
        "03": "护照",
    },

    isInMini() {
        // debugger
        return window.sessionStorage.getItem('inMini') == 'in'
    },


    // 退登
    // loginOut() {
    //     Toast({
    //         duration: 3000,
    //         forbidClick: true,
    //         message: "登录失效啦，即将重新登录~",
    //     });

    //     window.localStorage.removeItem("access_token")
    //     window.localStorage.removeItem("userInfo")
    //     // 保存当前路由路径，而不是完整URL
    //     const currentPath = window.location.hash || '#/';
    //     // window.sessionStorage.setItem("acURL", window.location.href)
    //     window.sessionStorage.setItem("acURL", currentPath)
    //     // window.location.href = "#/login"
    //     this.jump("#/login")
    // },





    sha256_(timeStamp, nonce) {

        let signingKey = "JlolWcxSD3fTdISQkEURIQ==";

        let salt_ = timeStamp % 10;
        let salt = nonce.substring(salt_);
        debugger;
        let stringSrc = signingKey + timeStamp + nonce + salt;
        return sha256(stringSrc);
    },

    getUrlParam: function (name) {
        var result = "";
        var url = window.location.href;
        // var url = (window.location.href);
        if (url.indexOf("?") > -1) {
            var param = url.substring(url.indexOf("?") + 1);
            // name += "=";
            let arr = param.split('&')
                .reduce(function (acc, param) {
                    const [key, value] = param.split('=');
                    acc[key] = decodeURIComponent(value);
                    return acc;
                }, {});
            /* if (param.indexOf(name) > -1) {
                var r = param.substr(param.indexOf(name) + name.length);
                if (r.indexOf("&") != -1) {
                    r = r.substring(0, r.indexOf("&"));
                }
                result = r;
            } */

            result = arr[name]

        }
        return result || "";
    },
    getUrlParam_(url, name) {
        var result = "";
        // var url = (window.location.href);
        /* if (url.indexOf("?") > -1) {
            var param = url.substring(url.indexOf("?") + 1);
            name += "=";
            if (param.indexOf(name) > -1) {
                var r = param.substr(param.indexOf(name) + name.length);
                if (r.indexOf("&") != -1) {
                    r = r.substring(0, r.indexOf("&"));
                }
                result = r;
            }
        }
        return result; */
        if (url.indexOf("?") > -1) {
            var param = url.substring(url.indexOf("?") + 1);
            let arr = param.split('&')
                .reduce(function (acc, param) {
                    const [key, value] = param.split('=');
                    acc[key] = decodeURIComponent(value);
                    return acc;
                }, {});
            result = arr[name]

        }
        return result;
    },

    updateUrlParam(url, name, value_) {
        debugger

        if (!url) {
            url = window.location.href;
        }

        if (url.indexOf("?") > -1) {
            var param = url.substring(url.indexOf("?") + 1);
            let arr = param.split('&');
            let obj = {};
            arr.forEach(element => {
                const [key, value] = element.split('=');
                if (key && value) {
                    if (key == name) {
                        obj[key] = value_;
                    } else {
                        obj[key] = decodeURIComponent(value);
                    }

                }
            });
            // obj[name] = value
            let str = "";
            for (let key in obj) {
                if (obj.hasOwnProperty(key)) {
                    str = str + key + "=" + obj[key] + "&";
                }
            }
            return url.substring(0, url.indexOf("?") + 1) + str;
        }

        return "";




    },

    toJSON(str) {
        return new Function("", "return " + str)();
    },


};

export default common;
