<template>
  <div class="search-container">
    <!-- 搜索头部 -->
    <div class="search-header">
      <!-- <van-nav-bar
        title="搜索活动"
        left-arrow
        @click-left="$router.back()"
      /> -->
      
      <div class="search-input-wrapper">
        <div class="custom-search-box">
          <div class="search-icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="#999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <input
            ref="searchInput"
            v-model="searchKeyword"
            type="text"
            placeholder="请输入活动名称或编号进行搜索"
            class="search-input"
            autocomplete="off"
            autocorrect="off"
            autocapitalize="off"
            spellcheck="false"
            @keyup.enter="handleSearch"
            @input="handleInput"
            @click="forceFocusSearchInput"
          />
          <button 
            class="search-button"
            @click="handleSearch"
          >
            搜索
          </button>
        </div>
      </div>
    </div>

    <!-- 搜索内容 -->
    <div class="search-content">
      <!-- 加载提示框 -->
      <div class="loading-container" v-if="showLoadingModal">
        <div class="loading-content">
        <div class="loading-spinner">
          <div class="spinner">
            <div class="spinner-bar" v-for="n in 12" :key="n" :style="{ transform: `rotate(${n * 30}deg)` }"></div>
          </div>
        </div>
          <div class="loading-text">数据加载中</div>
        </div>
      </div>

      <!-- 搜索历史 -->
      <!-- <div class="search-history" v-if="!searchKeyword && searchHistory.length > 0">
        <div class="history-header">
          <h3>搜索历史</h3>
          <van-button type="default" size="mini" @click="clearHistory">清空</van-button>
        </div>
        <div class="history-tags">
          <van-tag
            v-for="(item, index) in searchHistory"
            :key="index"
            type="default"
            @click="searchKeyword = item"
          >
            {{ item }}
          </van-tag>
        </div>
      </div>

      
      <div class="hot-search" v-if="!searchKeyword && hotSearchList.length > 0">
        <h3>热门搜索</h3>
        <div class="hot-tags">
          <van-tag
            v-for="(item, index) in hotSearchList"
            :key="index"
            :type="index < 3 ? 'primary' : 'default'"
            @click="searchKeyword = item"
          >
            {{ item }}
          </van-tag>
        </div>
      </div> -->

      <!-- 搜索结果 -->
      <div v-if="searchKeyword && hasSearched && !showLoadingModal">
        <!-- 活动类型分类tab -->
        <div class="category-tabs" v-if="categoryTabs.length > 0">
          <div class="tabs-scroll" ref="tabsScroll">
            <div 
              v-for="category in categoryTabs" 
              :key="category.id"
              class="tab-item"
              :class="{ active: activeCategory === category.id }"
              @click="switchCategory(category.id)"
            >
              {{ category.name }}
            </div>
          </div>
        </div>

        <!-- 活动列表 -->
        <div class="search-results">
          <div class="activity-list">
            <div 
              v-for="activity in filteredActivities" 
              :key="activity.id"
              class="activity-card"
              @click="goToActivityDetail(activity)"
            >
              <!-- 活动主图 -->
              <div class="activity-image-container">
                <img 
                  :src="activity.imageUrl" 
                  :alt="activity.title" 
                  class="activity-image"
                  @error="handleImageError"
                />
                <!-- 报名状态水印 -->
                <div 
                  v-if="activity.registrationStatus !== '报名进行中'" 
                  class="registration-overlay"
                >
                  <div class="status-watermark" :class="getStatusWatermarkClass(activity.registrationStatus)">
                    <div class="watermark-text">
                      <div class="watermark-line1">报名</div>
                      <div class="watermark-line2">{{ getRegistrationStatusText(activity.registrationStatus) }}</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 活动信息 -->
              <div class="activity-info">
                <h4 class="activity-title">{{ activity.title }}</h4>
                
                <!-- 活动时间 -->
                <div class="activity-time">
                  <span class="time-label">活动时间：</span>{{ formatActivityTime(activity.activityTime) }}
                </div>
                
                <!-- 底部信息行 -->
                <div class="bottom-info">
                  <!-- 报名情况 -->
                  <div class="registration-info">
                    <img src="@/assets/images/registration.png" alt="报名" class="registration-icon" />
                    报名人数：{{ activity.participantCount }}/{{ activity.maxParticipants }}
                  </div>
                  
                  <!-- 操作按钮 -->
                  <div class="action-buttons" v-if="shouldShowRegisterButton(activity)">
                    <van-button 
                      type="primary" 
                      size="small" 
                      class="register-button"
                      @click.stop="handleQuickRegister(activity)"
                    >
                      一键报名
                    </van-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="!showLoadingModal && filteredActivities.length === 0 && hasSearched" class="empty-state">
            <div class="empty-content">
              <div class="empty-image">
                <img src="@/assets/images/noAct.png" alt="没有活动信息" />
              </div>
              <div class="empty-text">没有活动信息～</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import {
  NavBar,
  Search,
  Button,
  Tag,
  Empty,
  Dialog,
  Toast
} from 'vant';
import auth from '@/utils/auth';
import util from '@/util/util';

Vue.use(NavBar)
  .use(Search)
  .use(Button)
  .use(Tag)
  .use(Empty)
  .use(Dialog)
  .use(Toast);

export default {
  name: 'SearchPage',
  data() {
    return {
      searchKeyword: '',
      searchHistory: [],
      hotSearchList: [],
      searchResults: [],
      hasSearched: false,
      totalCount: 0,
      showLoadingModal: false,
      // 新增的分类相关数据
      categoryTabs: [],
      activeCategory: 'all',
      allActivities: [], // 存储所有搜索结果
      categoryList: [] // 存储从接口获取的活动类型数据
    };
  },
  computed: {
    // 根据当前选中的分类过滤活动
    filteredActivities() {
      if (this.activeCategory === 'all') {
        return this.searchResults;
      }
      return this.searchResults.filter(activity => activity.categoryId === this.activeCategory);
    }
  },
  mounted() {
    this.loadSearchHistory();
    this.loadHotSearch();
    this.loadCategoryList(); // 加载活动类型数据

    // 如果有传入的搜索关键词，直接搜索
    if (this.$route.query.keyword) {
      this.searchKeyword = this.$route.query.keyword;
      this.handleSearch();
    } else {
      // 立即尝试聚焦，不使用延迟
      this.focusSearchInput();
    }
  },
  activated() {
    // 如果页面被keep-alive缓存，在激活时也尝试聚焦（仅当没有搜索内容时）
    if (!this.searchKeyword && !this.hasSearched) {
      this.focusSearchInput();
    }
  },
  methods: {
    // 聚焦搜索输入框
    focusSearchInput() {
      // 立即尝试聚焦，不使用延迟
      this.$nextTick(() => {
        if (this.$refs.searchInput) {
          try {
            // 确保输入框可见且可交互
            this.$refs.searchInput.style.pointerEvents = 'auto';
            this.$refs.searchInput.removeAttribute('readonly');
            this.$refs.searchInput.disabled = false;

            // 立即聚焦
            this.$refs.searchInput.focus();

            // 检查是否聚焦成功
            if (document.activeElement === this.$refs.searchInput) {
              console.log('搜索框聚焦成功');
            } else {
              console.log('搜索框聚焦失败，尝试其他方法');
              // 如果直接聚焦失败，尝试点击事件
              this.$refs.searchInput.click();

              // 再次尝试聚焦
              setTimeout(() => {
                this.$refs.searchInput.focus();
              }, 50);
            }

          } catch (error) {
            console.warn('自动聚焦失败:', error);
          }
        }
      });
    },

    // 强制聚焦方法（用于用户交互后调用）
    forceFocusSearchInput() {
      if (this.$refs.searchInput) {
        this.$refs.searchInput.focus();
        this.$refs.searchInput.click();
      }
    },

    loadSearchHistory() {
      // 从本地存储加载搜索历史
      const history = localStorage.getItem('activity_search_history');
      if (history) {
        this.searchHistory = JSON.parse(history);
      }
    },
    
    saveSearchHistory(keyword) {
      if (!keyword.trim()) return;
      
      // 移除重复项
      this.searchHistory = this.searchHistory.filter(item => item !== keyword);
      // 添加到开头
      this.searchHistory.unshift(keyword);
      // 限制历史记录数量
      this.searchHistory = this.searchHistory.slice(0, 10);
      
      // 保存到本地存储
      localStorage.setItem('activity_search_history', JSON.stringify(this.searchHistory));
    },
    
    clearHistory() {
      this.searchHistory = [];
      localStorage.removeItem('activity_search_history');
    },
    
    async loadHotSearch() {
      try {
        // TODO: 调用API获取热门搜索数据
        this.hotSearchList = [
          '理财讲座',
          '亲子研学',
          '健康体检',
          '微信支付礼',
          '金融必知必晓'
        ];
      } catch (error) {
        // 加载热门搜索失败
      }
    },

    // 加载活动类型数据
    async loadCategoryList() {
      try {
        // 调用API获取活动类型数据
        const params = {
          pageSize: 100 // 一次性获取所有活动类型
        };

        const response = await this.$api.getActivityCategoryList(params);

        // 适配不同的响应数据结构
        let categoryData = [];
        if (response && response.success === 1) {
          // 旧格式：{ success: 1, value: [...] }
          categoryData = response.value || [];
        } else if (response && response.code === 200 && response.data) {
          // 新格式：{ code: 200, data: { list: [...] } }
          categoryData = response.data.list || [];
        }

        if (categoryData.length > 0) {
          // 适配新的数据结构并按sort字段排序
          this.categoryList = categoryData.map(item => {
            return {
              id: item.typeId.toString(),
              name: item.typeTitle || '',
              sort: item.sort || 0
            };
          }).sort((a, b) => a.sort - b.sort); // 按sort字段升序排序，和首页保持一致
        } else {
          this.categoryList = [];
        }
      } catch (error) {
        console.error('加载活动类型失败:', error);
        this.categoryList = [];
      }
    },
    
    handleSearch() {
      // 检查输入内容是否为空或只包含空格
      if (!this.searchKeyword || !this.searchKeyword.trim()) {
        this.$toast('请输入有效搜索内容');
        return;
      }

      this.saveSearchHistory(this.searchKeyword.trim());
      this.resetSearchResults();
      // 立即显示加载状态，避免显示之前的结果
      this.showLoadingModal = true;
      this.loadSearchResults();
    },
    
    handleCancel() {
      this.searchKeyword = '';
      this.resetSearchResults();
    },
    
    handleClear() {
      this.searchKeyword = '';
      this.resetSearchResults();
    },
    
    handleInput() {
      // 输入时清空之前的结果，避免显示缓存
      if (this.hasSearched) {
        this.resetSearchResults();
      }
    },
    
    resetSearchResults() {
      this.searchResults = [];
      this.allActivities = [];
      this.categoryTabs = [];
      this.activeCategory = 'all';
      this.hasSearched = false;
      this.totalCount = 0;
    },
    
    async loadSearchResults() {
      if (!this.searchKeyword.trim()) return;

      this.hasSearched = true;

      try {
        // 1.8 首页搜索中间页接口调用 - 获取所有匹配的活动，不做分类筛选
        const params = {
          actTitle: this.searchKeyword.trim() // 只传活动名称搜索，不传typeTitle
        };

        const response = await this.$api.searchActivity(params, true);

        if (response && response.code === 200) {
          const activities = response.data || [];

          // 转换数据格式以适配页面显示
          const formattedActivities = activities.map(item => {
            // 处理多张图片的情况，只取第一张
            let imageUrl = '';
            if (item.headerImg) {
              // 如果包含多张图片（用逗号分隔），只取第一张
              const firstImage = item.headerImg.split(',')[0].trim();
              imageUrl = firstImage.startsWith('http') ? firstImage : process.env.VUE_APP_URL + firstImage;
            }

            return {
              id: item.id || item.actId,
              title: item.actTitle,
              imageUrl: imageUrl,
              headerImgId: item.headerImgId,
              activityStatus: item.activityStatus,
              registrationStatus: this.getRegistrationStatus(item),
              statusText: this.getActivityStatusText(item.activityStatus),
              statusClass: this.getStatusClass(item.activityStatus),
              activityTime: item.actTime,
              userRegistered: item.register === 1, // 1已报名，0未报名
              maxParticipants: item.numRage || 0, // 确保最大人数显示，默认为0
              participantCount: item.registerCount || 0, // 确保已报名人数显示，默认为0
              categoryId: String(item.typeId || 'all'), // 活动类型ID，确保为字符串类型
              categoryName: item.typeTitle || '', // 活动类型名称，用于分类匹配
              description: item.actTitle // 使用标题作为描述
            };
          });

          // 保存所有搜索结果
          this.allActivities = formattedActivities;
          this.totalCount = formattedActivities.length;
          this.generateCategoryTabs(formattedActivities);

          // 根据当前选中的分类筛选显示结果
          this.filterResultsByCategory();
        } else {
          this.searchResults = [];
          this.allActivities = [];
          this.totalCount = 0;
          this.categoryTabs = [];
        }
      } catch (error) {
        this.$toast('搜索失败，请重试');
        this.searchResults = [];
        this.allActivities = [];
        this.categoryTabs = [];
      } finally {
        this.showLoadingModal = false;
      }
    },
    

    getActivityStatusText(status) {
      const statusMap = {
        'not_started': '未开始',
        'ongoing': '进行中',
        'ended': '已结束',
        '0': '未开始',
        '1': '进行中',
        '2': '已结束'
      };
      return statusMap[status] || '未知状态';
    },

    getStatusClass(status) {
      const statusMap = {
        'not_started': 'not-started',
        'ongoing': 'ongoing',
        'ended': 'ended',
        '0': 'not-started',
        '1': 'ongoing',
        '2': 'ended'
      };
      return statusMap[status] || 'not-started';
    },

    goToActivityDetail(activity) {
      this.$router.push(`/activityDetail/${activity.id}`);
    },

    // 生成分类标签
    generateCategoryTabs(activities) {
      // 基于从接口获取的活动类型数据生成分类标签
      this.categoryTabs = [
        { id: 'all', name: '全部', count: activities.length }
      ];

      // 添加从接口获取的活动类型
      if (this.categoryList && this.categoryList.length > 0) {
        this.categoryList.forEach(category => {
          this.categoryTabs.push({
            id: category.id,
            name: category.name,
            count: 0
          });
        });
      } else {
        // 如果接口数据为空，使用默认的活动类型
        this.categoryTabs.push(
          { id: '1', name: '健康讲座', count: 0 },
          { id: '2', name: '亲子活动', count: 0 },
          { id: '3', name: '文体活动', count: 0 },
          { id: '4', name: '志愿服务', count: 0 },
          { id: '5', name: '教育培训', count: 0 }
        );
      }

      // 统计每个分类的活动数量
      activities.forEach(activity => {
        const activityCategoryName = activity.categoryName;
        if (activityCategoryName) {
          // 根据活动的分类名称找到对应的分类标签
          const category = this.categoryTabs.find(tab => tab.name === activityCategoryName);
          if (category) {
            category.count++;
          }
        }
      });
    },

    // 获取分类名称（这里需要根据实际业务逻辑调整）
    getCategoryName(categoryId) {
      const categoryNames = {
        '1': '健康讲座',
        '2': '亲子活动',
        '3': '文体活动',
        '4': '志愿服务',
        '5': '教育培训'
      };
      return categoryNames[categoryId] || `分类${categoryId}`;
    },

    // 切换分类
    switchCategory(categoryId) {
      this.activeCategory = categoryId;
      // 根据选中的分类筛选结果
      this.filterResultsByCategory();
    },

    // 根据当前选中的分类筛选搜索结果
    filterResultsByCategory() {
      if (this.activeCategory === 'all') {
        // 显示所有结果
        this.searchResults = [...this.allActivities];
      } else {
        // 根据分类名称（typeTitle）进行筛选
        const selectedCategoryName = this.categoryTabs.find(tab => tab.id === this.activeCategory)?.name;

        if (selectedCategoryName) {
          this.searchResults = this.allActivities.filter(activity => {
            return activity.categoryName === selectedCategoryName;
          });
        } else {
          this.searchResults = [];
        }
      }
    },

    // 获取报名状态
    getRegistrationStatus(item) {
      // 使用actRegisterStatus字段判断报名状态
      return item.actRegisterStatus;
    },

    // 获取报名状态文本
    getRegistrationStatusText(status) {
      // 去掉"报名"前缀，只保留状态文字
      if (status && status.includes('报名')) {
        return status.replace('报名', '');
      }
      return status || '已结束';
    },

    // 获取状态水印的CSS类名
    getStatusWatermarkClass(status) {
      if (status === '报名已结束') {
        return 'status-ended';
      } else if (status === '报名未开始') {
        return 'status-not-started';
      }
      return '';
    },

    // 格式化活动时间
    formatActivityTime(timeString) {
      if (!timeString) return '时间待定';
      
      try {
        // 处理时间字符串，支持多种格式
        let startTime, endTime;
        
        if (timeString.includes(' - ')) {
          [startTime, endTime] = timeString.split(' - ');
        } else if (timeString.includes(' 至 ')) {
          [startTime, endTime] = timeString.split(' 至 ');
        } else {
          return timeString; // 如果格式不匹配，直接返回原字符串
        }
        
        const formatDateTime = (dateTimeStr) => {
          const date = new Date(dateTimeStr.trim());
          if (isNaN(date.getTime())) return dateTimeStr;
          
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          
          const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
          const weekday = weekdays[date.getDay()];
          
          return `${year}.${month}.${day} 星期${weekday} ${hours}:${minutes}`;
        };
        
        const formattedStart = formatDateTime(startTime);
        const formattedEnd = formatDateTime(endTime);
        
        return `${formattedStart} 至 ${formattedEnd}`;
      } catch (error) {
        return timeString;
      }
    },

    // 判断是否显示报名按钮
    shouldShowRegisterButton(activity) {
      // 只有同时满足以下条件时才显示报名按钮：
      // 1. 报名进行中
      // 2. 当前人员未报名
      return activity.registrationStatus === '报名进行中' && !activity.userRegistered;
    },

    // 处理一键报名
    handleQuickRegister(activity) {
      // 检查登录状态
      if (!this.checkLoginStatus()) {
        this.showLoginDialog();
        return;
      }

      // 跳转到报名页面
      this.$router.push(`/activity/${activity.id}/registration`);
    },

    // 检查登录状态
    checkLoginStatus() {
      // 检查是否有token和用户信息
      const token = auth.getToken();
      const userInfo = localStorage.getItem('userInfo');
      return !!(token && userInfo);
    },

    // 显示登录对话框
    showLoginDialog() {
      this.$dialog.confirm({
        title: '提示',
        message: '您尚未登录，请先登录',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(() => {
        // 调用微信能力获取用户手机号
        this.getWechatPhone();
      }).catch(() => {
        // 用户取消
      });
    },

    // 获取微信手机号
    getWechatPhone() {
      // 保存当前页面路径，登录成功后跳转回来
      window.sessionStorage.setItem("acURL", this.$route.fullPath);
      // 跳转到登录页面
      util.jump('#/login');
    },

    // 处理图片加载错误
    handleImageError(event) {
      // 设置默认占位图片
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTE3IiBoZWlnaHQ9IjExNyIgdmlld0JveD0iMCAwIDExNyAxMTciIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMTciIGhlaWdodD0iMTE3IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00OC41IDQ4LjVIMTY4LjVWMTY4LjVINDguNVY0OC41WiIgZmlsbD0iI0NDQ0NDQyIvPgo8cGF0aCBkPSJNNjUgNjVIMTA1VjEwNUg2NVY2NVoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik03NSA3NUg5NVY4NUg3NVY3NVoiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+';
    }
  }
};
</script>

<style lang="less" scoped>
.search-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f8f8;
}

.search-header {
  background: white;
  border-radius: 8px 8px 0 0;
  
  .search-input-wrapper {
    margin-top: 8px;
    margin-left: 8px;
    margin-right: 8px;
    padding: 0 0 8px 0;

      .custom-search-box {
        display: flex;
        align-items: center;
        background-color: #f4f4f4;
        border-radius: 16px;
        height: 33px;
        overflow: hidden;
        
        .search-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          margin-left: 8px;
          flex-shrink: 0;
        }
 
        .search-input {
          flex: 1;
          height: 100%;
          border: none;
          outline: none;
          background: transparent;
          padding: 0 8px;
          font-size: 14px;
          color: #333;
 
          &::placeholder {
            color: #999;
          }
        }
 
        .search-button {
          flex-shrink: 0;
          min-width: 60px;
          padding: 0 12px;
          height: 33px;
          background-color: #cf9d60;
          border: none;
          border-radius: 16px;
          color: white;
          font-size: 12px;
          transition: background-color 0.3s ease;
          margin-left: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          white-space: nowrap;
          cursor: pointer;
        }
        
        .search-button:hover {
          background-color: #b8894f;
        }
        
        .search-button:active {
          background-color: #a67c4a;
        }
      }
  }
}

.search-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 0 16px 0;
}

.search-history, .hot-search {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  
  h3 {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 12px;
  }
  
  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  
  .history-tags, .hot-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    
    .van-tag {
      cursor: pointer;
    }
  }
}

// 分类标签样式
.category-tabs {
  background: white;
  margin-bottom: 16px;
  overflow: hidden;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  
  .tabs-scroll {
    display: flex;
    overflow-x: auto;
    padding: 0 16px;
    -webkit-overflow-scrolling: touch;
    
    &::-webkit-scrollbar {
      display: none;
    }
    
    .tab-item {
      flex-shrink: 0;
      padding: 12px 16px;
      font-size: 14px;
      color: #666;
      cursor: pointer;
      position: relative;
      white-space: nowrap;
      // 移除max-width限制，让分类名称完整显示
      // max-width: 80px;
      // overflow: hidden;
      // text-overflow: ellipsis;
      
      &.active {
        background: linear-gradient(90deg, #C08A48 0%, #E2B279 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 600;
        font-size: 15px;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 20px;
          height: 2px;
          background: linear-gradient(90deg, #C08A48 0%, #E2B279 100%);
          border-radius: 1px;
        }
      }
    }
  }
}

.search-results {
  position: relative;
  
  .result-stats {
    background: white;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    font-size: 14px;
    color: #666;
  }
  
  // 活动卡片样式
  .activity-card {
    background: white;
    border-radius: 6px;
    margin-bottom: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    width: 100%;
    min-height: 120px; // 设置最小高度，而不是固定高度
    height: auto; // 允许高度自动调整
    
    .activity-image-container {
      position: relative;
      width: 80px;
      height: 80px;
      flex-shrink: 0;
      overflow: hidden;
      margin: 16px 0 16px 16px; // 减少上下边距
      align-self: flex-start; // 改为顶部对齐，而不是居中
      
      .activity-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        border-radius: 6px;
      }
      
      // 报名状态蒙层
      .registration-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: transparent;
        pointer-events: none;
        
        .status-watermark {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          backdrop-filter: none;
          -webkit-backdrop-filter: none;
          
          .watermark-text {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            
            .watermark-line1 {
              font-size: 9px;
              font-weight: 500;
              line-height: 1;
              margin-bottom: 1px;
            }
            
            .watermark-line2 {
              font-size: 9px;
              font-weight: 500;
              line-height: 1;
            }
          }
          
          // 报名已结束样式
          &.status-ended {
            background: rgba(196, 196, 196, 0.5);
            color: black;
            box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1);
          }
          
          // 报名未开始样式
          &.status-not-started {
            background: rgba(0, 0, 0, 0.5);
            color: white;
            box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1);
          }
        }
      }
    }
    
    .activity-info {
      flex: 1;
      padding: 16px 16px 16px 12px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start; // 改为顶部对齐
      min-height: auto; // 移除最小高度限制，让内容自适应
      
      .activity-title {
        font-size: 15px;
        font-weight: 600;
        margin: 0 0 2px 0;
        line-height: 1.4;
        color: #333;
        word-break: break-word;
        overflow-wrap: break-word;
      }
      
      .activity-time {
        font-size: 12px;
        color: #666;
        margin-bottom: 12px; // 增加底部间距
        line-height: 1.4;

        .time-label {
          color: #999;
          margin-right: 4px;
        }
      }

      .bottom-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        min-height: 24px;
        margin-top: auto; // 让底部信息始终在底部
        
        .registration-info {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #666;
          
          .registration-icon {
            width: 14px;
            height: 14px;
            margin-right: 4px;
          }
        }
        
        .action-buttons {
          .register-button {
            background: linear-gradient(90deg, #C08A48 0%, #E2B279 100%);
            border: none;
            border-radius: 11.47px;
            color: white;
            font-size: 12px;
            padding: 4px 12px;
            width: 74px;
            height: 22.5px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }

  // 空状态样式
  .empty-state {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    min-height: 400px;
    
    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      
      .empty-image {
        margin-bottom: 10px;
        
        img {
          width: 220px;
          height: 220px;
          object-fit: contain;
        }
      }
      
      .empty-text {
        font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 16px;
        font-weight: 500;
        color: #CFCFCF;
        line-height: 30px;
        letter-spacing: 0px;
        text-align: center;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .search-header .search-input-wrapper {
    padding: 0 8px 12px;
    
    .custom-search-box {
      .search-button {
        width: 50px;
        font-size: 12px;
      }
    }
  }

  .category-tabs .tabs-scroll .tab-item {
    max-width: 60px;
    font-size: 13px;
    padding: 10px 12px;
  }

  .search-results {

    .empty-state {
      .empty-content {
        .empty-image {
          margin-bottom: 6px;
          
          img {
            width: 180px;
            height: 180px;
          }
        }

        .empty-text {
          font-size: 14px;
          line-height: 28px;
        }
      }
    }
  }
}

@media (min-width: 414px) {
  .search-header .search-input-wrapper {
    padding: 0 16px 12px;
    
    .custom-search-box {
      height: 33px;
      border-radius: 16px;
      
      .search-input {
        font-size: 15px;
      }
      
      .search-button {
        width: 70px;
        height: 33px;
        border-radius: 16px;
        font-size: 15px;
      }
    }
  }

}

/* 加载提示框样式 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  width: 100%;
  position: relative;
  padding-top: 60px;
}

.loading-content {
  background-color: #333;
  border-radius: 8px;
  padding: 25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
}

.loading-spinner {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 40px;
  height: 40px;
  position: relative;
  animation: spin 2s linear infinite;
}

.spinner-bar {
  position: absolute;
  width: 3px;
  height: 10px;
  background-color: #666;
  border-radius: 1.5px;
  top: 0;
  left: 50%;
  margin-left: -1.5px;
  transform-origin: 1.5px 20px;
}

.spinner-bar:nth-child(1) { animation-delay: 0s; background-color: #5e5e5e; }
.spinner-bar:nth-child(2) { animation-delay: -0.083s; background-color: #6e6e6e; }
.spinner-bar:nth-child(3) { animation-delay: -0.166s; background-color: #7e7e7e; }
.spinner-bar:nth-child(4) { animation-delay: -0.25s; background-color: #8e8e8e; }
.spinner-bar:nth-child(5) { animation-delay: -0.333s; background-color: #9e9e9e; }
.spinner-bar:nth-child(6) { animation-delay: -0.416s; background-color: #aeaeae; }
.spinner-bar:nth-child(7) { animation-delay: -0.5s; background-color: #bebebe; }
.spinner-bar:nth-child(8) { animation-delay: -0.583s; background-color: #cecece; }
.spinner-bar:nth-child(9) { animation-delay: -0.666s; background-color: #dedede; }
.spinner-bar:nth-child(10) { animation-delay: -0.75s; background-color: #dedede; }
.spinner-bar:nth-child(11) { animation-delay: -0.833s; background-color: #fefefe; }
.spinner-bar:nth-child(12) { animation-delay: -0.916s; background-color: #ffffff; }


.loading-text {
  color: #fff;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
