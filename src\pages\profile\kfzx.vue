<template>
    <div
        style="
            width: 100%;
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
            background: #38383b;
        "
    >
        <div class="bg">
            <!-- <p class="name">
                李丹妮<br /><span class="nameTip">客户关系管理岗</span>
            </p>

            <p class="bank">南京银行</p>

            <div class="ewm"></div> -->
        </div>

        <p class="btTip">扫描二维码，添加我的企业微信</p>
        <p class="btTipS">企业微信</p>
    </div>
</template>

<script>
import Vue from "vue";
import { Dialog, Toast, Uploader, List, PullRefresh } from "vant";
import { getMyActivities } from "@/api/my";
import util from "@/util/util";
Vue.use(Toast).use(Uploader).use(List).use(PullRefresh);

export default {
    name: "Profile",
    data() {
        return {};
    },
    mounted() {},
    methods: {},
};
</script>

<style lang="less" scoped>
.bg {
    width: 333px;
    height: 443px;
    margin: 20px auto;
    background: url("@/images/activity/kfBg.png") no-repeat top;
    background-size: 100%;
    position: relative;
    overflow: hidden;
    /* .name {
        font-weight: 500;
        font-size: 22px;
        color: #ffffff;
        margin-top: 243px;
        margin-left: 20px;
        .nameTip {
            font-weight: 400;
            font-size: 12px;
            color: #ffffff;
        }
    }
    .bank {
        font-weight: 500;
        font-size: 12px;
        color: #38383b;
        margin-top: 43px;
        margin-left: 20px;
    }
    .ewm {
        width: 0.9rem;
        height: 0.9rem;
        background: url("@/images/activity/ewm.png") no-repeat top;
        background-size: 100%;
        position: absolute;
        right: 24px;
        bottom: 20px;
    } */
}
.btTip {
    font-size: 10px;
    color: #b2b2b2;
    text-align: center;
    margin-top: 100px;
    margin-bottom: 20px;
    // position: fixed;
    width: 100%;
    // bottom: 120px;
}
.btTipS {
    font-size: 9px;
    color: #5f5f62;
    text-align: center;
    position: fixed;
    width: 100%;
    bottom: 50px;
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: center;
}
.btTipS::before {
    display: inline-block;
    content: "";
    width: 13px;
    height: 13px;
    background: url("@/images/activity/qyIcon.png") no-repeat center center;
    background-size: 100%;
    margin-right: 10px;
}
</style>
