<template>
  <div class="registration-detail-page">
    <!-- 导航栏 -->
    <!-- <van-nav-bar
      title="报名详情"
      left-arrow
      @click-left="goBack"
    /> -->


    <!-- 活动下架状态 -->
    <div v-if="false" class="delisted-container">
      <!-- 下架状态横幅 -->
      <div class="status-banner status-banner--delisted">
        <div class="status-icon">
          <img src="@/assets/images/registr.png" alt="状态图标" />
        </div>
        <div class="status-message">
          <div class="status-text status-text--delisted">活动已被下架，</div>
          <div class="status-text status-text--delisted">请及时关注官方公告信息！</div>
        </div>
      </div>
    </div>

    <!-- 正常状态内容 -->
    <div v-else class="content-container">
      <!-- 报名状态横幅 -->
      <div class="status-banner" :class="statusBannerClass">
        <div class="status-icon">
          <img src="@/assets/images/registr.png" alt="状态图标" />
        </div>
        <div class="status-message">
          <div class="status-text" :class="statusTextClass" v-html="statusMessage"></div>
          <div class="status-subtitle" v-if="statusSubtitle">{{ statusSubtitle }}</div>
        </div>
      </div>

      <!-- 报名信息 -->
      <div class="info-section">
        <div class="section-header">
          <div class="orange-bar"></div>
          <span class="section-title">报名信息</span>
        </div>
        <div class="info-content">
          <div 
            v-for="(item, index) in registerDetail.dtos" 
            :key="index" 
            class="info-item"
          >
            <span class="label">{{ item.title }}：</span>
            <span class="value">{{ item.result || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 活动详情 -->
      <div class="info-section">
        <div class="section-header">
          <div class="orange-bar"></div>
          <span class="section-title">活动详情</span>
        </div>
        <div class="info-content">
          <div class="info-item">
            <span class="label">活动名称：</span>
            <span class="value">{{ registerDetail.actTitle }}</span>
          </div>
          <div class="info-item">
            <span class="label">活动标题：</span>
            <span class="value">{{ registerDetail.actCaption }}</span>
          </div>
          <div class="info-item">
            <span class="label">活动时间：</span>
            <span class="value">{{ registerDetail.actTime }}</span>
          </div>
          <div class="info-item">
            <span class="label">活动地址：</span>
            <span class="value">{{ registerDetail.locationString }}</span>
          </div>
          <div class="info-item">
            <span class="label">活动须知：</span>
            <span class="value">{{ registerDetail.actNotice }}</span>
          </div>
        </div>
      </div>

      <!-- 活动介绍 -->
      <div class="info-section">
        <div class="section-header">
          <div class="orange-bar"></div>
          <span class="section-title">活动介绍</span>
        </div>
        <div class="info-content">
          <div class="rich-content" v-html="registerDetail.actDesc"></div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <van-button 
        v-if="showCancelButton"
        class="cancel-button"
        @click="handleCancelRegistration"
      >
        取消报名
      </van-button>
      <van-button 
        v-if="showSignInButton"
        class="signin-button"
        @click="handleSignIn"
      >
        活动签到
      </van-button>
      <van-button 
        v-if="showRegisterAgainButton"
        class="register-again-button"
        @click="handleRegisterAgain"
      >
        再次报名
      </van-button>
    </div>

    <!-- 取消报名确认弹窗 -->
    <van-dialog
      v-model="showCancelDialog"
      title="确认取消报名"
      message="确定取消报名？"
      show-cancel-button
      @confirm="confirmCancelRegistration"
      @cancel="showCancelDialog = false"
    />

    <!-- 签到组件 -->
    <acSignIn ref="acSignIn" />
  </div>
</template>

<script>
import Vue from 'vue';
import {
  NavBar,
  Button,
  Dialog,
  Field,
  Toast
} from 'vant';
import util from '@/util/util';
import acSignIn from '@/components/actSignIn/index.vue';
import { getRegisterDetail, cancelRegister } from '@/api/activity';

Vue.use(NavBar)
  .use(Button)
  .use(Dialog)
  .use(Field)
  .use(Toast);

export default {
  name: 'RegistrationDetail',
  components: {
    acSignIn
  },
  data() {
    return {
      // 接口返回的状态字符串
      activityStatus: '', // 活动状态
      registerStatus: '', // 报名状态
      userRegister: '', // 用户报名状态
      
      // 弹窗控制
      showCancelDialog: false,
      
      // 报名详情数据
      registerDetail: {
        id: null,
        actTitle: '',
        actTime: '',
        locationString: '',
        actNotice: '',
        actDesc: '',
        actCaption: '',
        dtos: [], // 报名信息列表
        activityUserDTO: {} // 用户信息
      },
      
      // 加载状态
      loading: false
    };
  },
  
  mounted() {
    this.loadRegisterDetail();
    if (util.getQueryStringHash("scanCode")) {
            this.$refs.acSignIn.signAc(
                util.getQueryStringHash("scanCode"),
                util.getQueryStringHash("acId")
            );
        }
  },
  
  computed: {
    // 状态横幅样式类
    statusBannerClass() {
      // 优先显示活动状态
      if (this.activityStatus === '已结束') {
        return 'status-banner--info';
      } else if (this.userRegister === '已报名') {
        return 'status-banner--success';
      } else if (this.userRegister === '已取消') {
        return 'status-banner--warning';
      }
      return 'status-banner--default';
    },
    
    // 状态文字样式类
    statusTextClass() {
      // 优先显示活动状态
      if (this.activityStatus === '已结束') {
        return 'status-text--info';
      } else if (this.userRegister === '已报名') {
        return 'status-text--success';
      } else if (this.userRegister === '已取消') {
        return 'status-text--warning';
      }
      return 'status-text--default';
    },
    
    // 状态消息
    statusMessage() {
      // 优先显示活动状态
      if (this.activityStatus === '已结束') {
        return '活动已结束，';
      } else if (this.userRegister === '已报名') {
        return '报名成功，';
      } else if (this.userRegister === '已取消') {
        return '<span style="color: #333;">您已</span><span style="color: #ee0a24;">取消</span><span style="color: #333;">报名！</span>';
      } else if (this.userRegister === '未报名') {
        return '未报名';
      }
      return '';
    },
    
    // 状态副标题
    statusSubtitle() {
      // 优先显示活动状态
      if (this.activityStatus === '已结束') {
        return '期待下次参与！';
      } else if (this.userRegister === '已报名' && this.activityStatus === '进行中') {
        return '请及时签到哦！';
      } else if (this.userRegister === '已报名' && this.activityStatus !== '已结束') {
        return '请及时签到哦！';
      }
      return '';
    },
    
    // 移除showActionButtons，直接使用条件判断
    
    // 是否显示取消报名按钮
    showCancelButton() {
      // 已报名且活动未结束时显示
      return this.userRegister === '已报名' && 
             this.activityStatus !== '已结束';
    },
    
    // 是否显示签到按钮
    showSignInButton() {
      // 已报名且活动进行中时显示
      return this.userRegister === '已报名' && 
             this.activityStatus === '进行中';
    },
    
    // 是否显示再次报名按钮
    showRegisterAgainButton() {
      // 未报名或已取消报名且活动未结束时显示
      return (this.userRegister === '未报名' || this.userRegister === '已取消') && 
             this.activityStatus !== '已结束';
    }
  },
  
  methods: {
    // 加载报名详情数据
    async loadRegisterDetail() {
      try {
        this.loading = true;
        const actId = this.$route.params.id;
        const response = await getRegisterDetail(actId);
        
        if (response && response.code === 200) {
          const data = response.data;
          this.registerDetail = data;
          this.activityStatus = data.activityStatus;
          this.registerStatus = data.registerStatus;
          this.userRegister = data.userRegister;
          
        }
      } catch (error) {
        console.error('加载报名详情失败:', error);
        Toast('加载数据失败');
      } finally {
        this.loading = false;
      }
    },
    
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    
    
    // 处理取消报名
    handleCancelRegistration() {
      this.showCancelDialog = true;
    },
    
    // 确认取消报名
    async confirmCancelRegistration() {
      try {
        this.showCancelDialog = false;
        const response = await cancelRegister(this.registerDetail.id);
        
        if (response && response.code === 200) {
          Toast('报名已取消');
          // 重新加载数据以更新状态
          this.loadRegisterDetail();
        } else {
          Toast(response.message || '取消报名失败');
        }
      } catch (error) {
        console.error('取消报名失败:', error);
        Toast('取消报名失败');
      }
    },
    
    // 处理签到
    handleSignIn() {
      // 调用签到组件，传入当前活动ID
      const activityId = this.$route.params.id; // 从路由参数获取活动ID
      this.$refs.acSignIn.showDia(activityId, 'input');
    },
    
    // 处理再次报名
    handleRegisterAgain() {
      // 跳转到报名页面
      util.jump(`#/activity/${this.$route.params.id}/registration`);
    }
  }
};
</script>

<style lang="less" scoped>
.registration-detail-page {
  min-height: 100vh;
  background: #f5f5f5;
  // 移除底部padding，按钮在页面内容中
}


// 活动下架状态 - 只显示banner，不需要额外的容器样式

// 内容容器 - 移除padding，让内容直接贴边

// 状态横幅
.status-banner {
  position: relative;
  background: url('@/assets/images/registr.png') no-repeat center;
  background-size: cover;
  padding: 32px 20px 0 20px; // 增加上方padding，去除下方padding
  margin: 0; // 移除所有间距
  overflow: hidden;
  min-height: 80px; // 增加最小高度
  
  .status-icon {
    position: absolute;
    top: 16px;
    right: 16px;
    
    img {
      width: 40px;
      height: 40px;
      opacity: 0.3;
    }
  }
  
  .status-message {
    position: relative;
    z-index: 1;
    
    .status-text {
      font-size: 18px;
      font-weight: 550;
      line-height: 1.4;
      margin-bottom: 4px;
      
      &--success {
        color: #07c160;
      }
      
      &--warning {
        color: #ff6b35;
      }
      
      &--info {
        color: #000000;
      }
      
      &--default {
        color: #333;
      }
      
      &--delisted {
        color: #000000;
      }
    }
    
    .status-subtitle {
      font-size: 18px;
      color: #000000;
      font-weight: 550;
    }
  }
  
  // 移除不同状态的背景色，统一使用图片背景
  
  // 下架状态使用默认样式
}

// 信息区块
.info-section {
  background: white;
  border-bottom: 1px dashed #e0e0e0; // 区块之间用虚线分隔
  
  &:last-child {
    border-bottom: none; // 最后一个区块不需要底部虚线
  }
  
  .section-header {
    display: flex;
    align-items: center;
    padding: 16px 16px 5px 16px;
    
    .orange-bar {
      width: 4px;
      height: 16px;
      background: #C28D4B;
      border-radius: 2px;
      margin-right: 8px;
    }
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }
  
  .info-content {
    padding: 0 16px 16px;
    
    .info-item {
      display: flex;
      // 移除信息项之间的分隔线和padding
      
      .label {
        font-family: 'PingFang SC', 'PingFang SC Regular', 'Microsoft YaHei', Arial, sans-serif;
        font-size: 11pt; // 11pt
        font-weight: 400; // Regular
        color: #9F9F9F; // #9F9F9F 100%
        flex-shrink: 0;
        text-align: left; // 左对齐
        margin-left: 12px; // 与section-title左对齐（orange-bar宽度4px + margin-right 8px = 12px）
        margin-right: 8px; // 与value保持适当距离
        line-height: 20pt; // 行高20pt
        letter-spacing: 0pt; // 字间距0pt
      }
      
      .value {
        font-family: 'PingFang SC', 'PingFang SC Regular', 'Microsoft YaHei', Arial, sans-serif;
        font-size: 11pt; // 11pt
        font-weight: 400; // Regular
        color: #9F9F9F; // #9F9F9F 100%
        word-break: break-all;
        line-height: 20pt; // 行高20pt
        letter-spacing: 0pt; // 字间距0pt
        flex: 1; // 移到后面，避免与margin-right冲突
      }
    }
    
    .rich-content {
      padding: 16px 0;
      margin-left: 12px; // 与section-title左对齐（orange-bar宽度4px + margin-right 8px = 12px）
      
      :deep(.rich-text) {
        p {
          margin: 8px 0;
          font-size: 14px;
          color: #333;
          line-height: 1.5;
          
          &:first-child {
            margin-top: 0;
          }
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

// 操作按钮
.action-buttons {
  background: white;
  border-top: 1px dashed #e0e0e0; // 虚线分隔
  padding: 24px 16px; // 增加上下padding，让信息块更高
  display: flex;
  justify-content: flex-end; // 右对齐
  align-items: center; // 垂直居中
  gap: 12px;
  min-height: 44px; // 设置最小高度，确保即使没有按钮也保持高度
  
  .van-button {
    width: 60pt; // 减小宽度
    height: 24pt; // 减小高度
    border-radius: 4pt; // 相应调整圆角
    font-size: 12px; // 减小字体
    font-weight: 500;
  }
  
  .cancel-button {
    background: white;
    border: 0.28pt solid #B7B7B7; // 0.28pt粗细，#B7B7B7颜色
    color: #666;
  }
  
  .signin-button {
    background: linear-gradient(90deg, #C08A48 0%, #EDC391 100%);
    border: none;
    color: white;
  }
  
  .register-again-button {
    background: #FFFAF7;
    border: 0.28pt solid #EFD9BE;
    border-radius: 4.5pt;
    color: #A8722F;
  }
}


// 移除响应式样式，使用统一样式
</style>
